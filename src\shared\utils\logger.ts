import pc from 'picocolors'

type LogLevel = 'info' | 'success' | 'warn' | 'error' | 'debug' | 'trade' | 'performance'

const EMOJIS: Record<LogLevel, string> = {
  info: 'ℹ️ ',
  success: '✅',
  warn: '⚠️ ',
  error: '❌',
  debug: '🐞',
  trade: '📈',
  performance: '⏱️'
}

const COLORS = {
  info: pc.blue,
  success: pc.green,
  warn: pc.yellow,
  error: pc.red,
  debug: pc.magenta,
  trade: pc.cyan,
  performance: pc.gray,
  timestamp: pc.gray,
  data: pc.white,
  origin: pc.blue
}

function formatData(data: unknown): string {
  if (typeof data === 'string') {
    return data
  }
  if (typeof data === 'object' && data !== null) {
    try {
      return JSON.stringify(data, null, 2)
    } catch {
      return pc.red('[Unserializable Object]')
    }
  }
  return String(data)
}

function log(level: LogLevel, origin: string, ...args: unknown[]): void {
  const timestamp = COLORS.timestamp(`[${new Date().toISOString()}]`)
  const emoji = EMOJIS[level]
  const levelColor = COLORS[level]
  const levelName = levelColor(level.toUpperCase())
  const originText = COLORS.origin(`[${origin}]`)

  const formattedArgs = args.map((arg) => {
    if (typeof arg === 'string') {
      return levelColor(arg)
    }
    return COLORS.data(formatData(arg))
  })

  console.log(`${timestamp} ${emoji} ${levelName} ${originText}`, ...formattedArgs)
}

export const logger = {
  info: (origin: string, ...args: unknown[]) => log('info', origin, ...args),
  success: (origin: string, ...args: unknown[]) => log('success', origin, ...args),
  warn: (origin: string, ...args: unknown[]) => log('warn', origin, ...args),
  error: (origin: string, ...args: unknown[]) => log('error', origin, ...args),
  debug: (origin: string, ...args: unknown[]) => log('debug', origin, ...args),
  trade: (origin: string, ...args: unknown[]) => log('trade', origin, ...args),
  performance: (origin: string, ...args: unknown[]) => log('performance', origin, ...args)
}
