import { io, Socket } from 'socket.io-client'
import { extractAuth } from '../../shared/utils/auth'
import { Region } from './../../shared/constants'
import { logger } from './../../shared/utils/logger'
import { BrowserWindow } from 'electron'
import { formatData } from '../../shared/utils/formatters'

export class PocketOption {
  private socket: Socket | null = null
  public static instance: PocketOption
  constructor(
    private ssID: string,
    private isDemo: boolean
  ) {}

  async connect(): Promise<void> {
    try {
      const endpoint = this.isDemo ? Region.DEMO_REGION : Region.getRegion()[0]
      const options = Region.SOCKET_OPTIONS
      this.socket = io(endpoint, options)
      const auth = extractAuth(this.ssID)
      const { session, uid } = auth

      this.socket.on('connect', () => {
        if (this.socket) {
          this.socket.emit('auth', {
            isDemo: this.isDemo ? 1 : 0,
            isFastHistory: true,
            platform: 2,
            session,
            uid
          })
        }
      })

      this.socket.on('successauth', () => {
        this.broacast(`connected`)
      })
    } catch (error) {
      logger.error(`Broker`, `Connection Error: ${(error as Error).message}`)
    }
  }

  private broacast(event: string, data?: unknown): void {
    let payload: unknown

    BrowserWindow.getAllWindows().forEach((window) => {
      payload = formatData(data as unknown[])
      if (Array.isArray(payload) && payload.length === 1) {
        payload = payload[0]
      }
      if (!window.isDestroyed()) {
        window.webContents.send('broker:event', event, payload)
      }
    })
  }
}
