import { io, Socket } from 'socket.io-client'
import { extractAuth } from '../../shared/utils/auth'
import { Region } from '../../shared/constants'
import { logger } from '../../shared/utils/logger'
import { BrowserWindow } from 'electron'
import { formatData } from '../../shared/utils/formatters'

/**
 * Interface for authentication data extracted from session ID
 */
interface AuthData {
  session: string
  uid: number
}

/**
 * Interface for socket authentication payload
 */
interface SocketAuthPayload {
  isDemo: number
  isFastHistory: boolean
  platform: number
  session: string
  uid: number
}

/**
 * Enum for connection states
 */
enum ConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * PocketOption broker class implementing Singleton pattern for managing WebSocket connections
 * to PocketOption trading platform. Handles authentication, connection management, and
 * event broadcasting to Electron renderer processes.
 */
export class PocketOption {
  private static _instance: PocketOption | null = null
  private socket: Socket | null = null
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED
  private reconnectAttempts: number = 0
  private readonly maxReconnectAttempts: number = 5
  private reconnectTimer: NodeJS.Timeout | null = null

  /**
   * Private constructor to enforce Singleton pattern
   * @param ssID - Session ID for authentication
   * @param isDemo - Whether to connect to demo environment
   */
  private constructor(
    private readonly ssID: string,
    private readonly isDemo: boolean
  ) {}

  /**
   * Gets the singleton instance of PocketOption
   * @param ssID - Session ID for authentication
   * @param isDemo - Whether to connect to demo environment
   * @returns The singleton instance
   */
  public static getInstance(ssID: string, isDemo: boolean): PocketOption {
    if (!PocketOption._instance) {
      PocketOption._instance = new PocketOption(ssID, isDemo)
    }
    return PocketOption._instance
  }

  /**
   * Destroys the singleton instance (useful for testing or cleanup)
   */
  public static destroyInstance(): void {
    if (PocketOption._instance) {
      PocketOption._instance.disconnect()
      PocketOption._instance = null
    }
  }

  /**
   * Gets the current connection state
   * @returns Current connection state
   */
  public getConnectionState(): ConnectionState {
    return this.connectionState
  }

  /**
   * Checks if the socket is currently connected
   * @returns True if connected, false otherwise
   */
  public isConnected(): boolean {
    return this.socket?.connected === true && this.connectionState === ConnectionState.CONNECTED
  }

  /**
   * Establishes connection to PocketOption WebSocket server
   * @throws Error if authentication data is invalid
   */
  public async connect(): Promise<void> {
    try {
      if (this.isConnected()) {
        logger.warn('Broker', 'Already connected to PocketOption')
        return
      }

      this.setConnectionState(ConnectionState.CONNECTING)

      const endpoint = this.getEndpoint()
      const options = Region.SOCKET_OPTIONS

      logger.info('Broker', `Connecting to ${endpoint}`)

      this.socket = io(endpoint, options)
      this.setupSocketEventHandlers()
    } catch (error) {
      this.setConnectionState(ConnectionState.ERROR)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Broker', `Connection Error: ${errorMessage}`)
      throw new Error(`Failed to connect to PocketOption: ${errorMessage}`)
    }
  }

  /**
   * Disconnects from the WebSocket server and cleans up resources
   */
  public disconnect(): void {
    try {
      this.clearReconnectTimer()

      if (this.socket) {
        this.socket.removeAllListeners()
        this.socket.disconnect()
        this.socket = null
      }

      this.setConnectionState(ConnectionState.DISCONNECTED)
      this.reconnectAttempts = 0

      logger.info('Broker', 'Disconnected from PocketOption')
      this.broadcast('disconnected')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Broker', `Disconnect Error: ${errorMessage}`)
    }
  }

  /**
   * Attempts to reconnect to the WebSocket server
   */
  public async reconnect(): Promise<void> {
    if (this.connectionState === ConnectionState.RECONNECTING) {
      logger.warn('Broker', 'Reconnection already in progress')
      return
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Broker', `Max reconnection attempts (${this.maxReconnectAttempts}) reached`)
      this.setConnectionState(ConnectionState.ERROR)
      return
    }

    this.setConnectionState(ConnectionState.RECONNECTING)
    this.reconnectAttempts++

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts - 1), 30000) // Exponential backoff, max 30s

    logger.info(
      'Broker',
      `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`
    )

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        logger.error('Broker', `Reconnection failed: ${errorMessage}`)

        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          await this.reconnect()
        } else {
          this.setConnectionState(ConnectionState.ERROR)
        }
      }
    }, delay)
  }

  /**
   * Gets the appropriate endpoint based on demo mode
   * @returns WebSocket endpoint URL
   */
  private getEndpoint(): string {
    return this.isDemo ? Region.DEMO_REGION : Region.getRegion(true)[0]
  }

  /**
   * Sets up all socket event handlers
   */
  private setupSocketEventHandlers(): void {
    if (!this.socket) return

    this.socket.on('connect', this.handleConnect.bind(this))
    this.socket.on('disconnect', this.handleDisconnect.bind(this))
    this.socket.on('connect_error', this.handleConnectError.bind(this))
    this.socket.on('successauth', this.handleSuccessAuth.bind(this))
    this.socket.on('error', this.handleError.bind(this))
  }

  /**
   * Handles socket connection event
   */
  private handleConnect(): void {
    logger.info('Broker', 'Socket connected, authenticating...')

    try {
      const authData = this.extractAuthData()
      const authPayload = this.createAuthPayload(authData)

      this.socket?.emit('auth', authPayload)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Broker', `Authentication failed: ${errorMessage}`)
      this.setConnectionState(ConnectionState.ERROR)
    }
  }

  /**
   * Handles socket disconnection event
   * @param reason - Reason for disconnection
   */
  private handleDisconnect(reason: string): void {
    logger.warn('Broker', `Socket disconnected: ${reason}`)
    this.setConnectionState(ConnectionState.DISCONNECTED)
    this.broadcast('disconnected', { reason })

    // Auto-reconnect for certain disconnect reasons
    if (reason === 'io server disconnect' || reason === 'transport close') {
      this.reconnect().catch((error) => {
        logger.error(
          'Broker',
          `Auto-reconnect failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        )
      })
    }
  }

  /**
   * Handles socket connection error event
   * @param error - Connection error
   */
  private handleConnectError(error: Error): void {
    logger.error('Broker', `Connection error: ${error.message}`)
    this.setConnectionState(ConnectionState.ERROR)
    this.broadcast('connection_error', { error: error.message })
  }

  /**
   * Handles successful authentication event
   */
  private handleSuccessAuth(): void {
    logger.success('Broker', 'Successfully authenticated with PocketOption')
    this.setConnectionState(ConnectionState.CONNECTED)
    this.reconnectAttempts = 0 // Reset reconnect attempts on successful connection
    this.broadcast('connected')
  }

  /**
   * Handles general socket error event
   * @param error - Socket error
   */
  private handleError(error: Error): void {
    logger.error('Broker', `Socket error: ${error.message}`)
    this.broadcast('error', { error: error.message })
  }

  /**
   * Extracts and validates authentication data from session ID
   * @returns Validated authentication data
   * @throws Error if authentication data is invalid
   */
  private extractAuthData(): AuthData {
    const authData = extractAuth(this.ssID)

    if (!authData || !authData.session || !authData.uid) {
      throw new Error('Invalid authentication data: missing session or uid')
    }

    return authData
  }

  /**
   * Creates authentication payload for socket
   * @param authData - Authentication data
   * @returns Socket authentication payload
   */
  private createAuthPayload(authData: AuthData): SocketAuthPayload {
    return {
      isDemo: this.isDemo ? 1 : 0,
      isFastHistory: true,
      platform: 2,
      session: authData.session,
      uid: authData.uid
    }
  }

  /**
   * Sets the connection state and logs the change
   * @param state - New connection state
   */
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      const previousState = this.connectionState
      this.connectionState = state
      logger.debug('Broker', `Connection state changed: ${previousState} -> ${state}`)
      this.broadcast('state_change', { from: previousState, to: state })
    }
  }

  /**
   * Clears the reconnection timer if it exists
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * Broadcasts events to all Electron renderer processes
   * @param event - Event name to broadcast
   * @param data - Optional data to send with the event
   */
  private broadcast(event: string, data?: unknown): void {
    try {
      const windows = BrowserWindow.getAllWindows()

      if (windows.length === 0) {
        logger.debug('Broker', 'No windows available for broadcasting')
        return
      }

      let payload: unknown = data

      // Format data if it's an array
      if (Array.isArray(data)) {
        payload = formatData(data)
        if (Array.isArray(payload) && payload.length === 1) {
          payload = payload[0]
        }
      }

      windows.forEach((window) => {
        if (!window.isDestroyed()) {
          window.webContents.send('broker:event', event, payload)
        }
      })

      logger.debug('Broker', `Broadcasted event '${event}' to ${windows.length} window(s)`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Broker', `Broadcast error: ${errorMessage}`)
    }
  }

	private 
}
